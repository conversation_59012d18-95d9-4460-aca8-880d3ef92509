/**
 * Authentication Signup Tests
 * 
 * Tests to verify the signup process works correctly
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock the API_CONFIG to use the correct endpoint
jest.mock('@/config/api', () => ({
  API_CONFIG: {
    BASE_URL: 'https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local',
    AUTH: {
      SIGNUP: '/auth/signup',
      VERIFY: '/confirm-user',
    },
    getUrl: (endpoint: string): string => {
      const base = 'https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local';
      const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
      return `${base}${path}`;
    }
  }
}));

describe('Signup API Tests', () => {
  beforeEach(() => {
    // Clear any previous mocks
    jest.clearAllMocks();
  });

  describe('API Configuration', () => {
    it('should have correct API base URL', () => {
      const { API_CONFIG } = require('@/config/api');
      expect(API_CONFIG.BASE_URL).toBe('https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local');
    });

    it('should generate correct signup URL', () => {
      const { API_CONFIG } = require('@/config/api');
      const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);
      expect(signupUrl).toBe('https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local/auth/signup');
    });
  });

  describe('Signup Process', () => {
    it('should handle successful signup response', async () => {
      // Mock successful response
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 201,
        json: jest.fn().mockResolvedValue({
          message: "User created successfully. Please check your email for verification code.",
          email: "<EMAIL>",
          role: "FREELANCER",
          requiresVerification: true
        })
      });

      const { API_CONFIG } = require('@/config/api');
      const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);

      const response = await fetch(signupUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPassword123!',
          name: 'Test User',
          role: 'FREELANCER',
        }),
      });

      expect(response.ok).toBe(true);
      expect(response.status).toBe(201);

      const result = await response.json();
      expect(result.requiresVerification).toBe(true);
      expect(result.email).toBe('<EMAIL>');
      expect(result.role).toBe('FREELANCER');
    });

    it('should handle signup errors correctly', async () => {
      // Mock error response
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 400,
        json: jest.fn().mockResolvedValue({
          error: 'ACCOUNT_EXISTS',
          message: 'An account with this email already exists'
        })
      });

      const { API_CONFIG } = require('@/config/api');
      const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);

      const response = await fetch(signupUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPassword123!',
          name: 'Test User',
          role: 'FREELANCER',
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);

      const result = await response.json();
      expect(result.error).toBe('ACCOUNT_EXISTS');
    });

    it('should handle network errors', async () => {
      // Mock network error
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const { API_CONFIG } = require('@/config/api');
      const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);

      await expect(
        fetch(signupUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'TestPassword123!',
            name: 'Test User',
            role: 'FREELANCER',
          }),
        })
      ).rejects.toThrow('Network error');
    });
  });

  describe('AuthService Integration', () => {
    it('should use correct API endpoint in AuthService', () => {
      // This test verifies that AuthService uses the correct API configuration
      const { API_CONFIG } = require('@/config/api');
      
      // Verify the configuration matches what AuthService expects
      expect(API_CONFIG.AUTH.SIGNUP).toBe('/auth/signup');
      expect(API_CONFIG.getUrl('/auth/signup')).toBe(
        'https://qj8altpvmi.execute-api.us-east-1.amazonaws.com/local/auth/signup'
      );
    });
  });

  describe('Environment Configuration', () => {
    it('should not use placeholder values', () => {
      const { API_CONFIG } = require('@/config/api');
      
      // Ensure we're not using placeholder values
      expect(API_CONFIG.BASE_URL).not.toBe('your_api_base_url');
      expect(API_CONFIG.BASE_URL).not.toContain('localhost:3000');
      expect(API_CONFIG.BASE_URL).toContain('amazonaws.com');
    });
  });
});

// Integration test helper
export const testSignupFlow = async (userData: {
  email: string;
  password: string;
  name: string;
  role: string;
}) => {
  const { API_CONFIG } = require('@/config/api');
  const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);

  try {
    const response = await fetch(signupUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const result = await response.json();

    return {
      success: response.ok,
      status: response.status,
      data: result,
      error: response.ok ? null : result.error || 'Unknown error'
    };
  } catch (error) {
    return {
      success: false,
      status: 0,
      data: null,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};
