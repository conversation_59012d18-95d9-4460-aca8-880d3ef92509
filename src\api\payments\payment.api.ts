import { graphQLClient } from '@/lib/graphql/graphqlClient';
import {
  CREATE_PAYMENT,
  UPDATE_PAYMENT,
  UPDATE_PAYMENT_STATUS,
} from './payment.mutations';
import {
  GET_PAYMENT,
  LIST_PAYMENTS,
  GET_PAYMENTS_BY_CONTRACT,
  GET_USER_PAYMENTS,
} from './payment.queries';
import {
  Payment,
  PaymentStatus,
  CreatePaymentDto,
  UpdatePaymentDto,
  PaymentFilters,
} from '@/types/features/payments/payment.types';

export interface CreatePaymentIntentRequest {
  amount: number;
  currency?: string;
  contractId: string;
  clientId: string;
  freelancerId: string;
  freelancerStripeAccountId?: string;
  platformFeePercentage?: number;
  metadata?: Record<string, string>;
}

export interface CreatePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  status: string;
  platformFeeAmount: number;
  freelancerAmount: number;
  hasDestinationCharge: boolean;
}

export interface ProcessRefundRequest {
  paymentIntentId: string;
  amount?: number;
  reason?: string;
  contractId: string;
  metadata?: Record<string, string>;
}

export interface RefundResponse {
  id: string;
  amount: number;
  currency: string;
  status: string;
  reason: string;
  created: number;
  metadata: Record<string, string>;
}

export const paymentApi = {
  /**
   * Create a payment intent with Stripe
   */
  createPaymentIntent: async (request: CreatePaymentIntentRequest): Promise<CreatePaymentIntentResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    console.log('Creating payment intent with request:', request);

    const response = await fetch(`${baseUrl}/api/payments/create-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    console.log('Payment intent API response status:', response.status);

    if (!response.ok) {
      let errorMessage = 'Failed to create payment intent';
      let errorDetails = null;
      
      try {
        const errorData = await response.json();
        console.error('Payment intent API error:', errorData);
        errorMessage = errorData.error || errorMessage;
        errorDetails = errorData.details || null;
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      
      throw new Error(`${errorMessage}${errorDetails ? ` - ${errorDetails}` : ''}`);
    }

    return await response.json();
  },

  /**
   * Get payment intent status from Stripe
   */
  getPaymentIntentStatus: async (paymentIntentId: string): Promise<{
    id: string;
    status: string;
    amount: number;
    currency: string;
    metadata: Record<string, string>;
    created: number;
    last_payment_error?: any;
  }> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const response = await fetch(
      `${baseUrl}/api/payments/create-intent?payment_intent_id=${paymentIntentId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get payment intent status');
    }

    return await response.json();
  },

  /**
   * Process a refund with Stripe
   */
  processRefund: async (request: ProcessRefundRequest): Promise<RefundResponse> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const response = await fetch(`${baseUrl}/api/payments/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to process refund');
    }

    return await response.json();
  },

  /**
   * Get refunds for a payment intent
   */
  getRefunds: async (paymentIntentId: string): Promise<{ refunds: RefundResponse[] }> => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';

    const response = await fetch(
      `${baseUrl}/api/payments/refund?payment_intent_id=${paymentIntentId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get refunds');
    }

    return await response.json();
  },


  /**
   * Create a payment record in the database
   */
  createPayment: async (input: CreatePaymentDto): Promise<Payment> => {
    const response = await graphQLClient.mutate<{ createPayment: Payment }>(
      CREATE_PAYMENT,
      { input },
      { authMode: 'userPool' }
    );
    return response.createPayment;
  },

  /**
   * Update a payment record
   */
  updatePayment: async (id: string, input: UpdatePaymentDto): Promise<Payment> => {
    const response = await graphQLClient.mutate<{ updatePayment: Payment }>(
      UPDATE_PAYMENT,
      { input: { id, ...input } },
      { authMode: 'userPool' }
    );
    return response.updatePayment;
  },

  /**
   * Update payment status
   */
  updatePaymentStatus: async (id: string, status: PaymentStatus, paidAt?: string): Promise<Payment> => {
    const response = await graphQLClient.mutate<{ updatePayment: Payment }>(
      UPDATE_PAYMENT_STATUS,
      {
        input: {
          id,
          status,
          paidAt: paidAt || new Date().toISOString()
        }
      },
      { authMode: 'userPool' }
    );
    return response.updatePayment;
  },

  /**
   * Get a payment by ID
   */
  getPayment: async (id: string): Promise<Payment> => {
    const response = await graphQLClient.query<{ getPayment: Payment }>(
      GET_PAYMENT,
      { id },
      { authMode: 'userPool' }
    );
    return response.getPayment;
  },

  /**
   * List payments with filters
   */
  listPayments: async (
    filters: PaymentFilters = {},
    limit: number = 10,
    nextToken?: string
  ): Promise<{ items: Payment[]; nextToken?: string }> => {
    const response = await graphQLClient.query<{ listPayments: { items: Payment[]; nextToken?: string } }>(
      LIST_PAYMENTS,
      { filter: filters, limit, nextToken },
      { authMode: 'userPool' }
    );
    return response.listPayments;
  },

  /**
   * Get payments for a specific contract
   */
  getPaymentsByContract: async (contractId: string): Promise<Payment[]> => {
    const response = await graphQLClient.query<{ listPayments: { items: Payment[] } }>(
      GET_PAYMENTS_BY_CONTRACT,
      { contractId },
      { authMode: 'userPool' }
    );
    return response.listPayments.items;
  },

  /**
   * Get payments for a user (either as client or freelancer)
   */
  getUserPayments: async (userId: string): Promise<Payment[]> => {
    try {
      // Query payments where user is either client or freelancer
      const clientPayments = await graphQLClient.query<{ listPayments: { items: Payment[] } }>(
        LIST_PAYMENTS,
        {
          filter: { clientId: { eq: userId } },
          limit: 100
        },
        { authMode: 'userPool' }
      );

      const freelancerPayments = await graphQLClient.query<{ listPayments: { items: Payment[] } }>(
        LIST_PAYMENTS,
        {
          filter: { freelancerId: { eq: userId } },
          limit: 100
        },
        { authMode: 'userPool' }
      );

      // Combine and deduplicate payments
      const allPayments = [
        ...clientPayments.listPayments.items,
        ...freelancerPayments.listPayments.items
      ];

      // Remove duplicates based on payment ID
      const uniquePayments = allPayments.filter((payment, index, self) =>
        index === self.findIndex(p => p.id === payment.id)
      );

      return uniquePayments;
    } catch (error) {
      console.error('Error fetching user payments:', error);
      // Return empty array if there's an error
      return [];
    }
  },
};