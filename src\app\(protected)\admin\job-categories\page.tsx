"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { formatDistanceToNow } from "date-fns";

import { useAuth } from "@/lib/auth/AuthContext";
import { jobCategoryService } from "@/api/job-categories/job-category.service";
import { Button, ConfirmDialog } from "@/components/ui";
import { Card, CardContent } from "@/components/ui/Card";
import { Table, Column } from "@/components/ui/Table";
import { Badge } from "@/components/ui/Badge";
import { Icon } from "@/components/ui";
import useToaster from "@/hooks/useToaster";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { ContentHeader } from "@/components/layout/ContentHeader";
import type {
  JobCategory,
  CreateJobCategoryInput,
  AdminJobCategory,
} from "@/types/features/job-categories/job-category.types";
import { ITEMS_PER_PAGE } from "@/types/common/pagination.constants";

const JobCategoriesAdminPage = () => {
  const {
    isAuthenticated,
    user,
    loading: authLoading,
    isInitialized,
  } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showSuccess, showError } = useToaster();
  const [categories, setCategories] = useState<JobCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState<JobCategory | null>(
    null
  );
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const [filters, setFilters] = useState({
    status: "" as "" | "ACTIVE" | "INACTIVE",
    sortBy: "newest" as "newest" | "name_asc" | "name_desc",
    search: "",
  });

  const memoizedFilters = useMemo(() => filters, [filters]);

  const [createFormData, setCreateFormData] = useState<CreateJobCategoryInput>({
    name: "",
    description: "",
    isActive: true,
  });

  const fetchCategories = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      setIsLoading(true);
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;

      const result = await jobCategoryService.listJobCategories(
        undefined,
        ITEMS_PER_PAGE * 3,
        undefined
      );

      let transformedCategories: JobCategory[] = result?.items || [];

      if (memoizedFilters.search.trim()) {
        const searchTerm = memoizedFilters.search.toLowerCase();
        transformedCategories = transformedCategories.filter(
          (cat) =>
            cat.name.toLowerCase().includes(searchTerm) ||
            (cat.description &&
              cat.description.toLowerCase().includes(searchTerm))
        );
      }

      if (memoizedFilters.status === "ACTIVE") {
        transformedCategories = transformedCategories.filter(
          (cat) => cat.isActive
        );
      } else if (memoizedFilters.status === "INACTIVE") {
        transformedCategories = transformedCategories.filter(
          (cat) => !cat.isActive
        );
      }

      if (memoizedFilters.sortBy === "name_asc") {
        transformedCategories.sort((a, b) => a.name.localeCompare(b.name));
      } else if (memoizedFilters.sortBy === "name_desc") {
        transformedCategories.sort((a, b) => b.name.localeCompare(a.name));
      } else {
        transformedCategories.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      }

      const totalItems = transformedCategories.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE) || 1;

      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, totalItems);
      const paginatedCategories = transformedCategories.slice(
        startIndex,
        endIndex
      );

      setCategories(paginatedCategories);
      setTotalItems(totalItems);
      setCurrentPage(validPage);

      if (page !== validPage) {
        const params = new URLSearchParams(searchParams);
        params.set("page", validPage.toString());
        router.replace(`?${params.toString()}`, { scroll: false });
      }
    } catch (err: unknown) {
      console.error("Error fetching job categories:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      showError(`Failed to fetch job categories: ${errorMessage}`);
      setCategories([]);
      setTotalItems(0);
      setCurrentPage(1);
      if (searchParams.get("page") !== "1") {
        router.replace("?page=1", { scroll: false });
      }
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, searchParams, router, memoizedFilters]); // showError excluded to prevent infinite re-renders

  useEffect(() => {
    const userRole = user?.attributes?.["custom:role"] || "ADMIN";

    const shouldCallFetchCategories =
      isAuthenticated && userRole === "ADMIN" && !authLoading && isInitialized;

    if (shouldCallFetchCategories) {
      fetchCategories();
    }
  }, [isAuthenticated, user, fetchCategories, authLoading, isInitialized]);

  const resetForms = useCallback(() => {
    setCreateFormData({
      name: "",
      description: "",
      isActive: true,
    });
  }, []);

  const handleCreateCategory = useCallback(async () => {
    try {
      await jobCategoryService.createJobCategory(createFormData);
      await fetchCategories();
      showSuccess("Job category created successfully");
      setShowCreateDialog(false);
      resetForms();
    } catch (err: unknown) {
      console.error("Error creating job category:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      showError(`Failed to create job category: ${errorMessage}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createFormData, fetchCategories, resetForms]); // showSuccess, showError excluded to prevent infinite re-renders

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedCategory) return;

    try {
      setDeleteLoading(selectedCategory.id.toString());
      await jobCategoryService.deleteJobCategory(selectedCategory.id);
      await fetchCategories();
      showSuccess("Job category deleted successfully");
    } catch (err: unknown) {
      console.error("Error deleting job category:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      showError(`Failed to delete job category: ${errorMessage}`);
    } finally {
      setDeleteLoading(null);
      setSelectedCategory(null);
      setShowDeleteDialog(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory, fetchCategories]); // showSuccess, showError excluded to prevent infinite re-renders

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
    setSelectedCategory(null);
  };

  const handlePageChange = (page: number) => {
    if (page !== currentPage) {
      const params = new URLSearchParams(searchParams);
      params.set("page", page.toString());
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const handleToggleStatus = async (category: JobCategory) => {
    try {
      await jobCategoryService.updateJobCategory({
        id: category.id,
        isActive: !category.isActive,
      });
      await fetchCategories();
      showSuccess(
        `Category ${
          !category.isActive ? "activated" : "deactivated"
        } successfully`
      );
    } catch (err: unknown) {
      console.error("Error toggling category status:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      showError(`Failed to update category status: ${errorMessage}`);
    }
  };

  const handleEdit = (category: JobCategory) => {
    router.push(`/admin/job-categories/${category.id}/edit`);
  };

  const handleView = (category: JobCategory) => {
    router.push(`/admin/job-categories/${category.id}`);
  };

  const handleDeleteClick = (category: JobCategory) => {
    setSelectedCategory(category);
    setShowDeleteDialog(true);
  };

  const columns: Column<JobCategory>[] = [
    {
      header: "Category Name",
      accessor: "name",
      sortable: true,
      cell: (value: unknown, row: JobCategory) => {
        const name = value as string;
        return (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-primary-100 text-primary-600">
              <Icon name="FolderOpen" className="h-5 w-5" />
            </div>
            <div className="flex flex-col">
              <span className="font-medium text-foreground">{name}</span>
              <span className="text-xs text-muted-foreground">
                {row.skills &&
                Array.isArray(row.skills) &&
                row.skills.length > 0
                  ? `${row.skills.length} skills`
                  : "No skills"}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      header: "Description",
      accessor: "description",
      sortable: false,
      cell: (value: unknown) => {
        const description = value as string;
        return (
          <span className="text-sm text-muted-foreground">
            {description || "No description"}
          </span>
        );
      },
    },
    {
      header: "Status",
      accessor: "isActive",
      sortable: true,
      cell: (value: unknown, row: JobCategory) => {
        const isActive = value as boolean;
        return (
          <div className="flex items-center gap-2">
            <Badge
              className={
                isActive
                  ? "bg-green-100 text-green-800"
                  : "bg-gray-100 text-gray-800"
              }
            >
              {isActive ? "Active" : "Inactive"}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleToggleStatus(row)}
              title={isActive ? "Deactivate" : "Activate"}
              className="h-6 w-6 p-0"
            >
              {isActive ? (
                <Icon name="EyeOff" className="h-4 w-4 text-yellow-600" />
              ) : (
                <Icon name="Eye" className="h-4 w-4 text-green-600" />
              )}
            </Button>
          </div>
        );
      },
    },
    {
      header: "Created",
      accessor: "createdAt",
      sortable: true,
      cell: (value: unknown) => {
        if (!value)
          return <span className="text-sm text-muted-foreground">N/A</span>;
        const dateValue = value as string | Date;
        const date =
          typeof dateValue === "string" ? new Date(dateValue) : dateValue;
        return (
          <span className="text-sm text-muted-foreground">
            {formatDistanceToNow(date, { addSuffix: true })}
          </span>
        );
      },
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (_: unknown, row: JobCategory) => {
        const handleViewClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleView(row);
        };

        const handleEditClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleEdit(row);
        };

        const onDeleteClick = (e: React.MouseEvent) => {
          e.stopPropagation();
          handleDeleteClick(row);
        };

        return (
          <div className="flex items-center justify-start space-x-1 w-full">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleViewClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="View Category"
            >
              <Icon name="Eye" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditClick}
              className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center"
              title="Edit Category"
            >
              <Icon name="Edit" size="sm" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onDeleteClick}
              disabled={deleteLoading === row.id}
              className="h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
              title="Delete Category"
            >
              {deleteLoading === row.id ? (
                <Icon name="Loader2" size="sm" className="animate-spin" />
              ) : (
                <Icon name="Trash2" size="sm" />
              )}
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const tablePagination = {
    enabled: true,
    currentPage,
    pageSize: ITEMS_PER_PAGE,
    totalItems,
    totalPages: Math.ceil(totalItems / ITEMS_PER_PAGE),
    onPageChange: handlePageChange,
    showFirstLast: true,
    showPrevNext: true,
    className: "mt-4",
  };

  if (!mounted || authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="Job Categories"
          subtitle="Manage and organize job categories used across the platform"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Job Categories", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
          <Button
            size="sm"
            onClick={() => router.push("/admin/job-categories/new")}
          >
            <Icon name="Plus" className="mr-2" />
            Add Job Category
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Search
                </label>
                <Input
                  placeholder="Search categories..."
                  value={filters.search}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value })
                  }
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Status
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.status}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      status: e.target.value as "" | "ACTIVE" | "INACTIVE",
                    })
                  }
                >
                  <option value="">All Status</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-muted-foreground mb-1">
                  Sort By
                </label>
                <select
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  value={filters.sortBy}
                  onChange={(e) =>
                    setFilters({
                      ...filters,
                      sortBy: e.target.value as
                        | "newest"
                        | "name_asc"
                        | "name_desc",
                    })
                  }
                >
                  <option value="newest">Newest First</option>
                  <option value="name_asc">Name (A-Z)</option>
                  <option value="name_desc">Name (Z-A)</option>
                </select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setFilters({
                    status: "",
                    sortBy: "newest",
                    search: "",
                  });
                }}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  fetchCategories();
                  setShowFilters(false);
                }}
              >
                Apply Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardContent className="p-6">
          <Table<AdminJobCategory>
            columns={columns}
            data={categories}
            isLoading={isLoading}
            pagination={tablePagination}
            emptyState={{
              title: "No job categories found",
              description: "No job categories match your current filters.",
              action: (
                <Button
                  onClick={() => router.push("/admin/job-categories/new")}
                >
                  <Icon name="Plus" className="mr-2" />
                  Add Job Category
                </Button>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Create Job Category Dialog */}
      <ConfirmDialog
        open={showCreateDialog}
        title="Create Job Category"
        onCancel={() => {
          setShowCreateDialog(false);
          resetForms();
        }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-0 w-full max-w-md"
        showFooter={false}
      >
        <div className="p-6 space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Fill in the details below to create a new job category.
          </p>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name *
              </label>
              <Input
                value={createFormData.name}
                onChange={(e) =>
                  setCreateFormData({ ...createFormData, name: e.target.value })
                }
                placeholder="Enter category name"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <Textarea
                value={createFormData.description || ""}
                onChange={(e) =>
                  setCreateFormData({
                    ...createFormData,
                    description: e.target.value,
                  })
                }
                placeholder="Enter category description"
                rows={3}
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <input
                type="checkbox"
                id="isActive"
                checked={createFormData.isActive}
                onChange={(e) =>
                  setCreateFormData({
                    ...createFormData,
                    isActive: e.target.checked,
                  })
                }
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label
                htmlFor="isActive"
                className="text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Active
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                resetForms();
              }}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateCategory}
              disabled={!createFormData.name?.trim()}
              className="min-w-[100px]"
            >
              Create Category
            </Button>
          </div>
        </div>
      </ConfirmDialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Job Category"
        message="Are you sure you want to delete this job category? This action cannot be undone and will permanently delete the category and all associated data."
        confirmText={deleteLoading ? "Deleting..." : "Delete"}
        cancelText="Cancel"
        confirmVariant="destructive"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={!!deleteLoading}
      />
    </div>
  );
};

export default JobCategoriesAdminPage;
