"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAuth } from '@/lib/auth/AuthContext';
import { PaymentDashboard } from '@/components/features/payments/PaymentDashboard';
import { UserRole } from '@/types/enums';
import { paymentTrackingService } from '@/api/payments/payment-tracking.service';

interface CommissionEarnings {
  totalCommissions: number;
  totalVolume: number;
  commissionRate: number;
  paymentCount: number;
  topFreelancers: Array<{
    freelancerId: string;
    freelancerName: string;
    totalVolume: number;
    totalCommissions: number;
  }>;
}

export default function AdminPaymentsPage() {
  const { user } = useAuth();
  const [refreshKey, setRefreshKey] = useState(0);
  const [commissionData, setCommissionData] = useState<CommissionEarnings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCommissionData();
  }, [refreshKey]);

  const loadCommissionData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await paymentTrackingService.getCommissionEarnings();
      setCommissionData(data);
    } catch (err) {
      console.error('Error loading commission data:', err);
      setError('Failed to load commission data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Payment Analytics</h1>
        <Button 
          variant="outline" 
          onClick={handleRefresh}
          className="flex items-center"
        >
          <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Commission Overview */}
      {commissionData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon name="dollar-sign" className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(commissionData.totalCommissions)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon name="trending-up" className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Volume</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(commissionData.totalVolume)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon name="percent" className="h-8 w-8 text-purple-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Commission Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {commissionData.commissionRate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon name="hash" className="h-8 w-8 text-orange-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Payments</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {commissionData.paymentCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Freelancers */}
      {commissionData?.topFreelancers && commissionData.topFreelancers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Top Freelancers by Volume</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {commissionData.topFreelancers.slice(0, 5).map((freelancer, index) => (
                <div key={freelancer.freelancerId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-medium">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{freelancer.freelancerName}</p>
                      <p className="text-sm text-gray-600">ID: {freelancer.freelancerId}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      {formatCurrency(freelancer.totalVolume)}
                    </p>
                    <p className="text-sm text-gray-600">
                      Commission: {formatCurrency(freelancer.totalCommissions)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Dashboard */}
      <PaymentDashboard 
        key={refreshKey}
        userRole={UserRole.ADMIN}
        userId={user?.id}
      />

      {/* Platform Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Insights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Average Transaction Size</span>
                <span className="font-medium">
                  {commissionData ? formatCurrency(commissionData.totalVolume / Math.max(commissionData.paymentCount, 1)) : '$0.00'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Average Commission per Transaction</span>
                <span className="font-medium">
                  {commissionData ? formatCurrency(commissionData.totalCommissions / Math.max(commissionData.paymentCount, 1)) : '$0.00'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Platform Fee Rate</span>
                <span className="font-medium">10.0%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment System Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Icon name="check-circle" className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Stripe Connect Active</p>
                  <p className="text-sm text-gray-600">Payment processing operational</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="check-circle" className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Webhooks Configured</p>
                  <p className="text-sm text-gray-600">Real-time payment updates</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="check-circle" className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Commission Tracking</p>
                  <p className="text-sm text-gray-600">Automatic fee calculation</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="check-circle" className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Payout Automation</p>
                  <p className="text-sm text-gray-600">Direct freelancer transfers</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="flex items-center justify-center">
              <Icon name="download" className="h-4 w-4 mr-2" />
              Export Payment Data
            </Button>
            <Button variant="outline" className="flex items-center justify-center">
              <Icon name="bar-chart" className="h-4 w-4 mr-2" />
              Generate Reports
            </Button>
            <Button variant="outline" className="flex items-center justify-center">
              <Icon name="settings" className="h-4 w-4 mr-2" />
              Payment Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <Icon name="alert-circle" className="h-5 w-5 text-red-600 mr-3" />
              <div>
                <p className="font-medium text-red-800">Error Loading Data</p>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
