import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { headers } from 'next/headers';

import { WebhookProcessor } from '@/utils/payments/webhookValidator';
import { paymentService } from '@/api/payments';
import { paymentApi } from '@/api/payments/payment.api';
import contractService from '@/api/contracts/contract.service';
import messageService from '@/api/messaging/message.service';
import { userService } from '@/api/users/user.service';

import { PaymentMethod, PaymentStatus, StripeAccountStatus } from '@/types/features/payments/payment.types';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { UserRole } from '@/types/enums';

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;
const webhookProcessor = new WebhookProcessor();

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = await headers();
    const sig = headersList.get('stripe-signature');

    if (!sig) {
      console.error('Missing stripe-signature header');
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    if (!endpointSecret) {
      console.error('Missing STRIPE_WEBHOOK_SECRET environment variable');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    const result = await webhookProcessor.processWebhook(
      body,
      sig,
      endpointSecret,
      handleStripeEvent
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleStripeEvent(event: any): Promise<void> {
  console.log(`Processing webhook event: ${event.type}`);

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.requires_action':
        await handlePaymentIntentRequiresAction(event.data.object as Stripe.PaymentIntent);
        break;

      case 'charge.dispute.created':
        await handleChargeDisputeCreated(event.data.object as Stripe.Dispute);
        break;

      case 'account.updated':
        await handleAccountUpdated(event.data.object as Stripe.Account);
        break;

      case 'transfer.created':
        await handleTransferCreated(event.data.object as Stripe.Transfer);
        break;

      case 'payout.paid':
        await handlePayoutPaid(event.data.object as Stripe.Payout);
        break;

      case 'payout.failed':
        await handlePayoutFailed(event.data.object as Stripe.Payout);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error(`Error handling ${event.type}:`, error);
    throw error;
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);

  try {
    const {
      contractId,
      clientId,
      freelancerId,
      platformFeePercentage,
      platformFeeAmount,
      freelancerAmount
    } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    const amount = paymentIntent.amount / 100;
    const platformFee = platformFeeAmount ? parseFloat(platformFeeAmount) : 0;
    const freelancerNet = freelancerAmount ? parseFloat(freelancerAmount) : amount;

    console.log(`Processing payment success for contract ${contractId}:`, {
      totalAmount: amount,
      platformFee,
      freelancerNet,
      hasTransfer: !!paymentIntent.transfer_data
    });

    // Create payment record with commission tracking
    const paymentData = {
      contractId,
      amount,
      method: PaymentMethod.STRIPE,
      status: PaymentStatus.PAID,
      currency: paymentIntent.currency.toUpperCase(),
      description: `Payment for contract ${contractId}`,
      stripePaymentIntentId: paymentIntent.id,
      stripeChargeId: paymentIntent.latest_charge as string,
      commissionAmount: platformFee,
      freelancerAmount: freelancerNet,
      platformFeeAmount: platformFee,
      platformFeePercentage: platformFeePercentage ? parseFloat(platformFeePercentage) : 10.0,
      clientId: clientId || '',
      freelancerId: freelancerId || ''
    };

    const paymentRecord = await paymentApi.createPayment(paymentData);

    console.log('Payment record created:', paymentRecord.id);

    const updatedContract = await contractService.updateContractStatus(
      contractId,
      ContractStatus.PAID
    );

    console.log('Contract status updated to PAID:', updatedContract.id);

    if (freelancerId && clientId) {
      try {
        const conversationId = await messageService.createConversation(
          updatedContract.jobId || 'payment-notification',
          clientId,
          freelancerId
        );

        await messageService.sendMessage(
          conversationId,
          'system',
          freelancerId,
          `🎉 Great news! Payment of $${amount.toFixed(2)} for contract "${updatedContract.title || contractId}" has been processed successfully. The funds will be available in your account according to our payment schedule.`
        );

        console.log('Notification sent to freelancer:', freelancerId);
      } catch (notificationError) {
        console.error('Failed to send notification to freelancer:', notificationError);
      }
    }

    const receiptData = {
      paymentId: paymentRecord.id,
      transactionId: paymentIntent.id,
      contractId: contractId,
      amount: amount,
      currency: paymentIntent.currency.toUpperCase(),
      paidAt: new Date().toISOString(),
      paymentMethod: 'Stripe',
      status: 'PAID',
      description: `Payment for contract ${contractId}`,
      clientData: clientId ? { id: clientId } : undefined,
      freelancerData: freelancerId ? { id: freelancerId } : undefined,
    };

    await paymentService.updatePaymentStatus(
      paymentRecord.id,
      PaymentStatus.PAID,
      paymentIntent.id,
      receiptData.paidAt
    );

    console.log('Payment processing completed successfully:', {
      paymentId: paymentRecord.id,
      contractId: contractId,
      amount: amount,
      transactionId: paymentIntent.id
    });

  } catch (error) {
    console.error('Error handling payment success:', error);
    console.error('Payment processing failed:', {
      paymentIntentId: paymentIntent.id,
      contractId: paymentIntent.metadata.contractId,
      amount: paymentIntent.amount,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment failed:', paymentIntent.id);

  try {
    const { contractId, clientId, freelancerId } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    const errorMessage = paymentIntent.last_payment_error?.message || 'Payment failed';
    const amount = paymentIntent.amount / 100;

    console.log(`Processing payment failure for contract ${contractId}: ${errorMessage}`);

    try {
      const existingPayments = await paymentService.getContractPayments(contractId);
      const pendingPayment = existingPayments.find(p =>
        p.transactionId === paymentIntent.id || p.status === PaymentStatus.PENDING
      );

      if (pendingPayment) {
        await paymentService.updatePaymentStatus(
          pendingPayment.id,
          PaymentStatus.FAILED,
          paymentIntent.id,
          new Date().toISOString()
        );
        console.log('Payment record updated to FAILED:', pendingPayment.id);
      }
    } catch (paymentUpdateError) {
      console.error('Failed to update payment record:', paymentUpdateError);
    }

    console.error('Payment failure details:', {
      contractId,
      paymentIntentId: paymentIntent.id,
      amount,
      errorCode: paymentIntent.last_payment_error?.code,
      errorMessage,
      errorType: paymentIntent.last_payment_error?.type,
    });

    if (clientId && freelancerId) {
      try {
        const conversationId = await messageService.createConversation(
          'payment-notification',
          clientId,
          freelancerId
        );

        await messageService.sendMessage(
          conversationId,
          'system',
          clientId,
          `⚠️ Payment failed for contract "${contractId}". Reason: ${errorMessage}. Please update your payment method and try again, or contact support if the issue persists.`
        );

        console.log('Payment failure notification sent to client:', clientId);
      } catch (notificationError) {
        console.error('Failed to send payment failure notification:', notificationError);
      }
    }

    const shouldRetry = paymentIntent.last_payment_error?.code === 'card_declined' ||
      paymentIntent.last_payment_error?.code === 'insufficient_funds';

    if (shouldRetry) {
      console.log('Payment failure is retryable, consider implementing retry logic');
    }

  } catch (error) {
    console.error('Error handling payment failure:', error);
  }
}

async function handlePaymentIntentCanceled(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment canceled:', paymentIntent.id);

  try {
    const { contractId, clientId, freelancerId } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    console.log(`Processing payment cancellation for contract ${contractId}`);

    try {
      const existingPayments = await paymentService.getContractPayments(contractId);
      const pendingPayment = existingPayments.find(p =>
        p.transactionId === paymentIntent.id || p.status === PaymentStatus.PENDING
      );

      if (pendingPayment) {
        await paymentService.updatePaymentStatus(
          pendingPayment.id,
          PaymentStatus.CANCELLED,
          paymentIntent.id,
          new Date().toISOString()
        );
        console.log('Payment record updated to CANCELLED:', pendingPayment.id);
      }
    } catch (paymentUpdateError) {
      console.error('Failed to update payment record:', paymentUpdateError);
    }

    if (clientId && freelancerId) {
      try {
        const conversationId = await messageService.createConversation(
          'payment-notification',
          clientId,
          freelancerId
        );

        await messageService.sendMessage(
          conversationId,
          'system',
          clientId,
          `🚫 Payment for contract "${contractId}" has been cancelled. You can initiate a new payment when ready.`
        );

        console.log('Payment cancellation notification sent');
      } catch (notificationError) {
        console.error('Failed to send cancellation notification:', notificationError);
      }
    }

  } catch (error) {
    console.error('Error handling payment cancellation:', error);
  }
}

async function handlePaymentIntentRequiresAction(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment requires action:', paymentIntent.id);

  try {
    const { contractId } = paymentIntent.metadata;

    if (!contractId) {
      console.error('No contractId in payment intent metadata');
      return;
    }

    // TODO: Handle additional authentication requirements
    // This typically happens with 3D Secure or other authentication methods
    console.log(`Payment requires action for contract ${contractId}`);

  } catch (error) {
    console.error('Error handling payment action requirement:', error);
  }
}

async function handleChargeDisputeCreated(dispute: Stripe.Dispute) {
  console.log('Charge dispute created:', dispute.id);

  try {
    const disputeAmount = dispute.amount / 100;
    const chargeId = dispute.charge as string;
    const disputeReason = dispute.reason || 'unknown';
    const evidenceDeadline = dispute.evidence_details?.due_by ? new Date(dispute.evidence_details.due_by * 1000) : null;
    
    console.log(`Dispute created for charge ${chargeId}, amount: $${disputeAmount}, reason: ${disputeReason}`);

    const allPayments = await paymentService.listPayments({}, 50);
    const existingPayments = allPayments.items.filter(p => p.transactionId === chargeId);
    
    let contractId: string | undefined;
    let clientId: string | undefined;
    let freelancerId: string | undefined;
    
    if (existingPayments.length > 0) {
      const payment = existingPayments[0];
      contractId = payment.contractId;
      
      if (contractId) {
        try {
          const contract = await contractService.getContract(contractId);
          clientId = contract.clientId;
          freelancerId = contract.freelancerId;
        } catch (contractError) {
          console.error('Failed to get contract details for dispute:', contractError);
        }
      }
    }

    try {
      const adminUsers = await userService.listUsers({ role: UserRole.ADMIN });
      
      if (adminUsers.items.length > 0) {
        const adminId = adminUsers.items[0].id;
        const adminConversationId = await messageService.createConversation(
          `dispute-${dispute.id}`,
          adminId,
          adminId
        );

        const disputeMessage = `🚨 **Payment Dispute Alert**\n\n` +
          `**Dispute ID:** ${dispute.id}\n` +
          `**Charge ID:** ${chargeId}\n` +
          `**Amount:** $${disputeAmount}\n` +
          `**Reason:** ${disputeReason}\n` +
          `**Status:** ${dispute.status}\n` +
          `${contractId ? `**Contract:** ${contractId}\n` : ''}` +
          `${evidenceDeadline ? `**Evidence Due:** ${evidenceDeadline.toLocaleString()}\n` : ''}` +
          `\n**Action Required:** Please review this dispute in the Stripe dashboard and gather necessary evidence.`;

        await messageService.sendMessage(
          adminConversationId,
          'system',
          adminId,
          disputeMessage
        );

        console.log('Admin notification sent for dispute:', dispute.id);
      }
    } catch (adminNotificationError) {
      console.error('Failed to notify admin about dispute:', adminNotificationError);
    }

    const evidenceData = {
      disputeId: dispute.id,
      chargeId: chargeId,
      contractId: contractId,
      disputeReason: disputeReason,
      disputeAmount: disputeAmount,
      evidenceDeadline: evidenceDeadline?.toISOString(),
      gatheringInstructions: {
        requiredDocuments: [
          'Service agreement/contract',
          'Proof of service delivery',
          'Communication records with customer',
          'Delivery confirmations',
          'Customer signature or approval'
        ],
        stripeEvidenceFields: {
          access_activity_log: 'User activity logs if applicable',
          billing_address: 'Customer billing address',
          cancellation_policy: 'Service cancellation policy',
          cancellation_policy_disclosure: 'How policy was disclosed',
          customer_communication: 'All customer communications',
          customer_email_address: 'Customer email address',
          customer_name: 'Customer full name',
          customer_purchase_ip: 'Customer IP address during purchase',
          customer_signature: 'Digital or physical signature',
          duplicate_charge_documentation: 'Evidence charge is not duplicate',
          duplicate_charge_explanation: 'Explanation of separate charges',
          duplicate_charge_id: 'ID of original charge if applicable',
          product_description: 'Detailed service description',
          receipt: 'Receipt or invoice',
          refund_policy: 'Refund policy',
          refund_policy_disclosure: 'How refund policy was disclosed',
          refund_refusal_explanation: 'Why refund was refused if applicable',
          service_date: 'Date service was provided',
          service_documentation: 'Proof of service delivery',
          shipping_address: 'Service delivery address',
          shipping_carrier: 'Delivery method',
          shipping_date: 'Date of delivery',
          shipping_documentation: 'Delivery confirmation',
          shipping_tracking_number: 'Tracking number if applicable',
          uncategorized_file: 'Additional supporting documentation',
          uncategorized_text: 'Additional explanatory text'
        },
        evidenceSubmissionSteps: [
          '1. Log into Stripe Dashboard',
          '2. Navigate to Payments → Disputes',
          `3. Find dispute ${dispute.id}`,
          '4. Click "Submit Evidence"',
          '5. Upload all relevant documentation',
          '6. Fill in required text fields',
          '7. Submit before the deadline'
        ]
      },
      createdAt: new Date().toISOString()
    };

    console.log('Evidence gathering data prepared:', {
      disputeId: dispute.id,
      contractId: contractId,
      evidenceDeadline: evidenceDeadline?.toISOString(),
      requiredDocumentsCount: evidenceData.gatheringInstructions.requiredDocuments.length
    });

    if (contractId && clientId && freelancerId) {
      try {
        await contractService.updateContractStatus(contractId, ContractStatus.DISPUTED);
        console.log(`Contract ${contractId} status updated to DISPUTED due to payment dispute`);

        if (existingPayments.length > 0) {
          const payment = existingPayments[0];
          
          await paymentService.updatePaymentStatus(
            payment.id,
            PaymentStatus.FAILED,
            payment.transactionId,
            payment.paidAt
          );

          console.log('Payment status updated due to dispute:', payment.id);
        }

        try {
          const disputeNotificationConversationId = await messageService.createConversation(
            `dispute-notification-${contractId}`,
            clientId,
            freelancerId
          );

          const disputeNotificationMessage = `⚠️ **Payment Dispute Notice**\n\n` +
            `A payment dispute has been filed for this contract.\n\n` +
            `**Dispute Details:**\n` +
            `• Amount: $${disputeAmount}\n` +
            `• Reason: ${disputeReason}\n` +
            `• Contract Status: Updated to "In Dispute"\n\n` +
            `The contract has been temporarily suspended while the dispute is being resolved. ` +
            `Our team will review the case and work to resolve this matter as quickly as possible.\n\n` +
            `If you have any questions or additional information, please contact support.`;

          await messageService.sendMessage(
            disputeNotificationConversationId,
            'system',
            clientId,
            disputeNotificationMessage
          );
          
          await messageService.sendMessage(
            disputeNotificationConversationId,
            'system',
            freelancerId,
            disputeNotificationMessage
          );

          console.log('Dispute notifications sent to both parties');
        } catch (notificationError) {
          console.error('Failed to send dispute notifications to parties:', notificationError);
        }

      } catch (statusUpdateError) {
        console.error('Failed to update contract/payment status for dispute:', statusUpdateError);
      }
    }

    console.log('Dispute handling completed:', {
      disputeId: dispute.id,
      chargeId: chargeId,
      contractId: contractId,
      amount: disputeAmount,
      reason: disputeReason,
      adminNotified: true,
      evidencePrepared: true,
      statusUpdated: !!contractId,
      partiesNotified: !!(contractId && clientId && freelancerId)
    });

  } catch (error) {
    console.error('Error handling dispute creation:', error);
    
    console.error('Dispute handling failed:', {
      disputeId: dispute.id,
      chargeId: dispute.charge,
      amount: dispute.amount,
      reason: dispute.reason,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
}

// Stripe Connect webhook handlers
async function handleAccountUpdated(account: Stripe.Account) {
  console.log('Account updated:', account.id);

  try {
    const userId = account.metadata?.userId;

    if (!userId) {
      console.log('No userId in account metadata, skipping user update');
      return;
    }

    // Update user's Stripe account status
    await userService.updateStripeAccountInfo(userId, {
      stripeAccountStatus: account.charges_enabled && account.payouts_enabled ? StripeAccountStatus.ACTIVE : StripeAccountStatus.PENDING,
      stripeChargesEnabled: account.charges_enabled || false,
      stripePayoutsEnabled: account.payouts_enabled || false,
      stripeDetailsSubmitted: account.details_submitted || false,
      stripeOnboardingComplete: account.charges_enabled && account.payouts_enabled
    });

    console.log('User Stripe account status updated:', {
      userId,
      accountId: account.id,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted
    });

  } catch (error) {
    console.error('Error handling account update:', {
      accountId: account.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
}

async function handleTransferCreated(transfer: Stripe.Transfer) {
  console.log('Transfer created:', transfer.id);

  try {
    const paymentIntentId = transfer.source_transaction;

    if (!paymentIntentId) {
      console.log('No source transaction in transfer, skipping');
      return;
    }

    // Log transfer details for tracking
    console.log('Transfer details:', {
      transferId: transfer.id,
      amount: transfer.amount / 100,
      currency: transfer.currency,
      destination: transfer.destination,
      sourceTransaction: paymentIntentId
    });

  } catch (error) {
    console.error('Error handling transfer creation:', {
      transferId: transfer.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
}

async function handlePayoutPaid(payout: Stripe.Payout) {
  console.log('Payout paid:', payout.id);

  try {
    // Log payout information for tracking
    console.log('Payout details:', {
      payoutId: payout.id,
      amount: payout.amount / 100,
      currency: payout.currency,
      status: payout.status,
      arrivalDate: payout.arrival_date,
      method: payout.method,
      type: payout.type
    });

  } catch (error) {
    console.error('Error handling payout paid:', {
      payoutId: payout.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
}

async function handlePayoutFailed(payout: Stripe.Payout) {
  console.log('Payout failed:', payout.id);

  try {
    // Log payout failure for investigation
    console.error('Payout failure details:', {
      payoutId: payout.id,
      amount: payout.amount / 100,
      currency: payout.currency,
      status: payout.status,
      failureCode: payout.failure_code,
      failureMessage: payout.failure_message
    });

  } catch (error) {
    console.error('Error handling payout failure:', {
      payoutId: payout.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
}