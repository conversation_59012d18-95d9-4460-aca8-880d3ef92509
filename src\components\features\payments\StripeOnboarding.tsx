"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAuth } from '@/lib/auth/AuthContext';
import { userService } from '@/api/users/user.service';
import { stripeConnectApi } from '@/api/stripe/stripe-connect.api';

interface StripeOnboardingProps {
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface OnboardingStatus {
  hasAccount: boolean;
  needsOnboarding: boolean;
  status: string;
  onboardingComplete: boolean;
  chargesEnabled: boolean;
  payoutsEnabled: boolean;
  detailsSubmitted: boolean;
  requirements?: any;
  currentlyDue?: string[];
  eventuallyDue?: string[];
}

export const StripeOnboarding: React.FC<StripeOnboardingProps> = ({
  onComplete,
  onError
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<OnboardingStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      checkOnboardingStatus();
    }
  }, [user?.id]);

  const checkOnboardingStatus = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const statusData = await stripeConnectApi.getOnboardingStatus(user.id);
      setStatus(statusData);
      
      if (statusData.onboardingComplete && onComplete) {
        onComplete();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to check onboarding status';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const createStripeAccount = async () => {
    if (!user?.id || !user?.email) return;

    try {
      setLoading(true);
      setError(null);

      await stripeConnectApi.createAccount({
        userId: user.id,
        email: user.email,
        country: 'US'
      });

      // Refresh status after account creation
      await checkOnboardingStatus();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create Stripe account';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const startOnboarding = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const returnUrl = `${window.location.origin}/dashboard/payments/onboarding?success=true`;
      const refreshUrl = `${window.location.origin}/dashboard/payments/onboarding?refresh=true`;

      const { url } = await stripeConnectApi.createOnboardingLink({
        userId: user.id,
        returnUrl,
        refreshUrl
      });

      // Redirect to Stripe onboarding
      window.location.href = url;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start onboarding';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (!status) return 'clock';
    
    if (status.onboardingComplete) {
      return 'check-circle';
    } else if (status.hasAccount) {
      return 'clock';
    } else {
      return 'x-circle';
    }
  };

  const getStatusColor = () => {
    if (!status) return 'text-gray-500';
    
    if (status.onboardingComplete) {
      return 'text-green-600';
    } else if (status.hasAccount) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  };

  const getStatusText = () => {
    if (!status) return 'Checking status...';
    
    if (status.onboardingComplete) {
      return 'Ready to receive payments';
    } else if (status.hasAccount && status.needsOnboarding) {
      return 'Complete your account setup';
    } else if (status.hasAccount) {
      return 'Account created, pending verification';
    } else {
      return 'Account setup required';
    }
  };

  if (loading && !status) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Icon name="loader" className="h-6 w-6 animate-spin mr-2" />
            <span>Checking payment account status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="credit-card" className="h-5 w-5 mr-2" />
          Payment Account Setup
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="flex">
              <Icon name="alert-circle" className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-3">
          <Icon 
            name={getStatusIcon()} 
            className={`h-6 w-6 ${getStatusColor()}`} 
          />
          <div>
            <p className="font-medium">{getStatusText()}</p>
            {status && (
              <p className="text-sm text-gray-600">
                Status: {status.status}
              </p>
            )}
          </div>
        </div>

        {status && status.onboardingComplete && (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center">
              <Icon name="check-circle" className="h-5 w-5 text-green-400 mr-2" />
              <div>
                <p className="text-green-800 font-medium">Account Ready!</p>
                <p className="text-green-700 text-sm">
                  You can now receive payments from clients.
                </p>
              </div>
            </div>
          </div>
        )}

        {status && status.currentlyDue && status.currentlyDue.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex">
              <Icon name="alert-triangle" className="h-5 w-5 text-yellow-400 mr-2" />
              <div>
                <p className="text-yellow-800 font-medium">Action Required</p>
                <p className="text-yellow-700 text-sm">
                  Please complete the following requirements:
                </p>
                <ul className="text-yellow-700 text-sm mt-1 list-disc list-inside">
                  {status.currentlyDue.map((requirement, index) => (
                    <li key={index}>{requirement.replace(/_/g, ' ')}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="flex space-x-3">
          {!status?.hasAccount && (
            <Button 
              onClick={createStripeAccount}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Icon name="loader" className="h-4 w-4 animate-spin mr-2" />
                  Creating Account...
                </>
              ) : (
                <>
                  <Icon name="plus" className="h-4 w-4 mr-2" />
                  Create Payment Account
                </>
              )}
            </Button>
          )}

          {status?.hasAccount && status?.needsOnboarding && (
            <Button 
              onClick={startOnboarding}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Icon name="loader" className="h-4 w-4 animate-spin mr-2" />
                  Starting Setup...
                </>
              ) : (
                <>
                  <Icon name="external-link" className="h-4 w-4 mr-2" />
                  Complete Account Setup
                </>
              )}
            </Button>
          )}

          <Button 
            variant="outline" 
            onClick={checkOnboardingStatus}
            disabled={loading}
          >
            <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </div>

        {status?.hasAccount && (
          <div className="text-sm text-gray-600 space-y-1">
            <p>Account Details:</p>
            <ul className="space-y-1 ml-4">
              <li>• Charges enabled: {status.chargesEnabled ? '✓' : '✗'}</li>
              <li>• Payouts enabled: {status.payoutsEnabled ? '✓' : '✗'}</li>
              <li>• Details submitted: {status.detailsSubmitted ? '✓' : '✗'}</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
