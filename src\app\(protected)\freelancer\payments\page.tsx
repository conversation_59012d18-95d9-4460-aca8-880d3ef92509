"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAuth } from '@/lib/auth/AuthContext';
import { PaymentDashboard } from '@/components/features/payments/PaymentDashboard';
import { StripeOnboarding } from '@/components/features/payments/StripeOnboarding';
import { UserRole } from '@/types/enums';
import { stripeConnectApi } from '@/api/stripe/stripe-connect.api';

export default function FreelancerPaymentsPage() {
  const { user } = useAuth();
  const [onboardingStatus, setOnboardingStatus] = useState<{
    hasAccount: boolean;
    needsOnboarding: boolean;
    onboardingComplete: boolean;
    status: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      checkOnboardingStatus();
    }
  }, [user?.id]);

  const checkOnboardingStatus = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      const status = await stripeConnectApi.getOnboardingStatus(user.id);
      setOnboardingStatus(status);
    } catch (err) {
      console.error('Error checking onboarding status:', err);
      setError('Failed to check payment setup status');
    } finally {
      setLoading(false);
    }
  };

  const handleOnboardingComplete = () => {
    checkOnboardingStatus();
  };

  const handleOnboardingError = (error: string) => {
    setError(error);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Payments & Earnings</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Payments & Earnings</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Icon name="alert-circle" className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Payment Information</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={checkOnboardingStatus}>
                <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Payments & Earnings</h1>
        <Button 
          variant="outline" 
          onClick={checkOnboardingStatus}
          className="flex items-center"
        >
          <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Payment Setup Status */}
      {onboardingStatus && !onboardingStatus.onboardingComplete && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center text-yellow-800">
              <Icon name="alert-triangle" className="h-5 w-5 mr-2" />
              Payment Setup Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-yellow-700 mb-4">
              To receive payments from clients, you need to set up your payment account with Stripe.
              This is a one-time setup that allows you to receive payments directly to your bank account.
            </p>
            <StripeOnboarding 
              onComplete={handleOnboardingComplete}
              onError={handleOnboardingError}
            />
          </CardContent>
        </Card>
      )}

      {/* Payment Setup Complete */}
      {onboardingStatus?.onboardingComplete && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <Icon name="check-circle" className="h-5 w-5 text-green-600 mr-3" />
              <div>
                <p className="font-medium text-green-800">Payment Setup Complete</p>
                <p className="text-green-700 text-sm">
                  You're ready to receive payments from clients. Funds will be automatically transferred to your bank account.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Dashboard */}
      <PaymentDashboard 
        userRole={UserRole.FREELANCER}
        userId={user?.id}
      />

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">How Payments Work</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Clients pay for completed work through the platform</li>
                <li>• Platform fee (10%) is automatically deducted</li>
                <li>• You receive 90% of the payment amount</li>
                <li>• Funds are transferred directly to your bank account</li>
                <li>• Payouts follow Stripe's standard schedule</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Payment Schedule</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Payments are processed immediately when clients pay</li>
                <li>• Bank transfers typically take 2-7 business days</li>
                <li>• You'll receive email notifications for all transactions</li>
                <li>• View detailed payment history in your dashboard</li>
                <li>• Download statements for tax purposes</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-2">Need Help?</h4>
            <p className="text-sm text-gray-600">
              If you have questions about payments or need to update your banking information, 
              you can manage your payment settings through your Stripe dashboard or contact our support team.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
