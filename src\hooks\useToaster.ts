import { useCallback } from 'react';
import { useToast } from '@/components/ui/toast';

type ToastOptions = {
  duration?: number;
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
};

export const useToaster = () => {
  const { showToast } = useToast();

  const showSuccess = useCallback((message: string, options: ToastOptions = {}) => {
    showToast(message, {
      type: 'success',
      position: 'top-right',
      ...options,
    });
  }, [showToast]);

  const showError = useCallback((message: string, options: ToastOptions = {}) => {
    showToast(message, {
      type: 'error',
      position: 'top-right',
      ...options,
    });
  }, [showToast]);

  const showLoading = useCallback((message: string, options: ToastOptions = {}) => {
    showToast(message, {
      type: 'loading',
      position: 'top-right',
      ...options,
    });
  }, [showToast]);

  const showInfo = useCallback((message: string, options: ToastOptions = {}) => {
    showToast(message, {
      type: 'success',
      position: 'top-right',
      ...options,
    });
  }, [showToast]);

  const showWarning = useCallback((message: string, options: ToastOptions = {}) => {
    showToast(message, {
      type: 'error',
      position: 'top-right',
      ...options,
    });
  }, [showToast]);

  return {
    showSuccess,
    showError,
    showLoading,
    showInfo,
    showWarning,
  };
};

export default useToaster;
