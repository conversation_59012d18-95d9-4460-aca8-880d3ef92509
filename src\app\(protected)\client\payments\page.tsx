"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAuth } from '@/lib/auth/AuthContext';
import { PaymentDashboard } from '@/components/features/payments/PaymentDashboard';
import { UserRole } from '@/types/enums';

export default function ClientPaymentsPage() {
  const { user } = useAuth();
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Payment History</h1>
        <Button 
          variant="outline" 
          onClick={handleRefresh}
          className="flex items-center"
        >
          <Icon name="refresh-cw" className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Payment Information */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-start">
            <Icon name="info" className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
            <div>
              <p className="font-medium text-blue-800">Secure Payment Processing</p>
              <p className="text-blue-700 text-sm">
                All payments are processed securely through Stripe. Your payment information is encrypted and protected.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Dashboard */}
      <PaymentDashboard 
        key={refreshKey}
        userRole={UserRole.CLIENT}
        userId={user?.id}
      />

      {/* Payment Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>How Payments Work</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm font-medium">1</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Contract Completion</p>
                  <p className="text-sm text-gray-600">
                    When a freelancer completes work, you'll receive a notification to review and approve the deliverables.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm font-medium">2</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Payment Processing</p>
                  <p className="text-sm text-gray-600">
                    Once approved, you can process payment securely through our platform using your preferred payment method.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm font-medium">3</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Automatic Transfer</p>
                  <p className="text-sm text-gray-600">
                    The freelancer receives their payment (minus platform fee) directly to their bank account.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Icon name="shield" className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-gray-900">Secure Processing</p>
                  <p className="text-sm text-gray-600">Bank-level security with Stripe</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="clock" className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-gray-900">Instant Payments</p>
                  <p className="text-sm text-gray-600">Payments are processed immediately</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="file-text" className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium text-gray-900">Transaction History</p>
                  <p className="text-sm text-gray-600">Complete payment records and receipts</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="credit-card" className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium text-gray-900">Multiple Payment Methods</p>
                  <p className="text-sm text-gray-600">Credit cards, debit cards, and more</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Icon name="download" className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="font-medium text-gray-900">Export Records</p>
                  <p className="text-sm text-gray-600">Download statements for accounting</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Fee Information */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Fee Structure</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-gray-900">10%</p>
                <p className="text-sm text-gray-600">Platform Fee</p>
                <p className="text-xs text-gray-500 mt-1">Automatically deducted</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">90%</p>
                <p className="text-sm text-gray-600">To Freelancer</p>
                <p className="text-xs text-gray-500 mt-1">Direct bank transfer</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-600">100%</p>
                <p className="text-sm text-gray-600">You Pay</p>
                <p className="text-xs text-gray-500 mt-1">Total project amount</p>
              </div>
            </div>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                Example: For a $1,000 project, you pay $1,000. The freelancer receives $900, and $100 goes to platform fees.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Support Information */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Payment Issues</h4>
              <p className="text-sm text-gray-600 mb-3">
                If you encounter any issues with payments or need to dispute a transaction, 
                our support team is here to help.
              </p>
              <Button variant="outline" size="sm">
                <Icon name="mail" className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Payment Methods</h4>
              <p className="text-sm text-gray-600 mb-3">
                We accept all major credit cards and debit cards. Payment methods are securely stored 
                and can be updated in your account settings.
              </p>
              <Button variant="outline" size="sm">
                <Icon name="settings" className="h-4 w-4 mr-2" />
                Manage Payment Methods
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
